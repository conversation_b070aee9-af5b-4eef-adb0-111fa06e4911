#!/bin/bash

# AEC Pack Dispensing Inspection System 管理脚本
# 用法: ./run.sh {start|stop|restart|status|test}

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/aec-inference.pid"
LOG_FILE="$SCRIPT_DIR/error.log"
MAIN_SCRIPT="$SCRIPT_DIR/main.py"
CONFIG_FILE="$SCRIPT_DIR/config/default.json"

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 启动服务
start_server() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "AEC Inference Service 已经在运行中 (PID: $pid)"
        return 1
    fi

    echo "启动 AEC Inference Service..."
    cd "$SCRIPT_DIR"

    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "警告: 配置文件不存在 ($CONFIG_FILE)，使用默认配置"
    fi

    # 使用新的main.py启动服务，指定配置文件（前台模式但通过nohup后台运行）
    nohup python "$MAIN_SCRIPT" start --config "$CONFIG_FILE" 1>/dev/null 2>"$LOG_FILE" &
    local pid=$!
    echo $pid > "$PID_FILE"

    # 等待一下检查是否启动成功
    sleep 3
    if is_running; then
        echo "AEC Inference Service 启动成功 (PID: $pid)"
        echo "日志文件: $LOG_FILE"
        echo "访问地址: http://localhost:8000"
        return 0
    else
        echo "AEC Inference Service 启动失败，请检查日志: $LOG_FILE"
        return 1
    fi
}

# 停止服务
stop_server() {
    if ! is_running; then
        echo "AEC Inference Service 未运行"
        return 1
    fi

    local pid=$(cat "$PID_FILE")
    echo "停止 AEC Inference Service (PID: $pid)..."

    # 尝试使用新的服务管理器停止
    cd "$SCRIPT_DIR"
    python "$MAIN_SCRIPT" stop --config "$CONFIG_FILE" 2>/dev/null

    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            rm -f "$PID_FILE"
            echo "AEC Inference Service 已停止"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    # 如果优雅停止失败，强制停止
    echo "强制停止 AEC Inference Service..."
    kill -9 "$pid" 2>/dev/null
    rm -f "$PID_FILE"
    echo "AEC Inference Service 已强制停止"
    return 0
}

# 重启服务
restart_server() {
    echo "重启 AEC Inference Service..."
    stop_server
    sleep 2
    start_server
}

# 查看状态
show_status() {
    echo "=== AEC Inference Service 状态 ==="

    # 使用新的服务管理器获取状态
    cd "$SCRIPT_DIR"
    python "$MAIN_SCRIPT" status --config "$CONFIG_FILE" 2>/dev/null

    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo ""
        echo "进程信息:"
        echo "PID: $pid"
        echo "日志文件: $LOG_FILE"

        # 显示进程信息
        if command -v ps > /dev/null; then
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || echo "无法获取进程详细信息"
        fi

        # 健康检查
        echo ""
        echo "健康检查:"
        if command -v curl > /dev/null; then
            local health_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health 2>/dev/null)
            if [ "$health_status" = "200" ]; then
                echo "✅ 服务健康 (HTTP 200)"
            else
                echo "❌ 服务异常 (HTTP $health_status)"
            fi
        else
            echo "⚠️  无法执行健康检查 (curl未安装)"
        fi

        # 显示最近的日志
        if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
            echo ""
            echo "最近的错误日志 (最后10行):"
            tail -10 "$LOG_FILE"
        fi
    else
        echo ""
        echo "❌ AEC Inference Service 未运行"

        # 如果有日志文件，显示最近的错误
        if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
            echo ""
            echo "最近的错误日志 (最后5行):"
            tail -5 "$LOG_FILE"
        fi
    fi
}

# 运行测试
run_tests() {
    echo "=== 运行 AEC Inference Service 测试 ==="
    cd "$SCRIPT_DIR"

    # 检查测试脚本是否存在
    if [ ! -f "scripts/run_tests.py" ]; then
        echo "❌ 测试脚本不存在: scripts/run_tests.py"
        return 1
    fi

    echo "🧪 运行单元测试和集成测试..."
    echo "ℹ️  测试数据将自动标记为测试数据（通过User-Agent识别）"
    python scripts/run_tests.py
    local test_result=$?

    if [ $test_result -eq 0 ]; then
        echo "✅ 所有测试通过"
    else
        echo "❌ 部分测试失败"
    fi

    return $test_result
}

# 运行API接口测试
test_apis() {
    echo "=== 测试所有API接口 ==="

    # 检查服务是否运行
    if ! is_running; then
        echo "❌ 服务未运行，请先启动服务"
        return 1
    fi

    # 检查curl是否可用
    if ! command -v curl > /dev/null; then
        echo "❌ curl未安装，无法执行API测试"
        return 1
    fi

    echo "ℹ️  测试数据将自动标记为测试数据（通过User-Agent识别）"
    echo ""

    local base_url="http://localhost:8000"
    local failed_tests=0
    local total_tests=0

    echo "基础URL: $base_url"
    echo ""

    # 测试健康检查
    echo "🔍 测试健康检查..."
    total_tests=$((total_tests + 1))
    local health_response=$(curl -s -w "\n%{http_code}" -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/health")
    local health_code=$(echo "$health_response" | tail -n1)
    if [ "$health_code" = "200" ]; then
        echo "✅ 健康检查通过 (HTTP $health_code)"
    else
        echo "❌ 健康检查失败 (HTTP $health_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试主页
    echo "🏠 测试主页..."
    total_tests=$((total_tests + 1))
    local index_code=$(curl -s -o /dev/null -w "%{http_code}" -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/")
    if [ "$index_code" = "200" ]; then
        echo "✅ 主页访问成功 (HTTP $index_code)"
    else
        echo "❌ 主页访问失败 (HTTP $index_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试统计API
    echo "📊 测试统计API..."
    total_tests=$((total_tests + 1))
    local stats_code=$(curl -s -o /dev/null -w "%{http_code}" -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/api/statistics")
    if [ "$stats_code" = "200" ]; then
        echo "✅ 统计API访问成功 (HTTP $stats_code)"
    else
        echo "❌ 统计API访问失败 (HTTP $stats_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试误报记录API
    echo "🔄 测试误报记录API..."
    total_tests=$((total_tests + 1))
    local fp_code=$(curl -s -o /dev/null -w "%{http_code}" -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/api/false-positive/reports")
    if [ "$fp_code" = "200" ]; then
        echo "✅ 误报记录API访问成功 (HTTP $fp_code)"
    else
        echo "❌ 误报记录API访问失败 (HTTP $fp_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试误报浏览器页面
    echo "🖼️  测试误报浏览器..."
    total_tests=$((total_tests + 1))
    local viewer_code=$(curl -s -o /dev/null -w "%{http_code}" -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/false-positive-viewer")
    if [ "$viewer_code" = "200" ]; then
        echo "✅ 误报浏览器访问成功 (HTTP $viewer_code)"
    else
        echo "❌ 误报浏览器访问失败 (HTTP $viewer_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试推理API (无文件上传)
    echo "🤖 测试推理API (无文件)..."
    total_tests=$((total_tests + 1))
    local predict_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" "$base_url/predict")
    if [ "$predict_code" = "400" ]; then
        echo "✅ 推理API正确拒绝无文件请求 (HTTP $predict_code)"
    else
        echo "❌ 推理API响应异常 (HTTP $predict_code)"
        failed_tests=$((failed_tests + 1))
    fi

    # 测试误报上报API (无数据)
    echo "📝 测试误报上报API..."
    total_tests=$((total_tests + 1))
    local report_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST -H "X-Test-Request: true" -H "User-Agent: AEC-Test-Script/1.0" -H "Content-Type: application/json" "$base_url/api/false-positive/report")
    if [ "$report_code" = "400" ]; then
        echo "✅ 误报上报API正确拒绝无数据请求 (HTTP $report_code)"
    else
        echo "❌ 误报上报API响应异常 (HTTP $report_code)"
        failed_tests=$((failed_tests + 1))
    fi

    echo ""
    echo "=== 测试结果 ==="
    echo "总测试数: $total_tests"
    echo "成功: $((total_tests - failed_tests))"
    echo "失败: $failed_tests"

    if [ $failed_tests -eq 0 ]; then
        echo "🎉 所有API测试通过!"
        return 0
    else
        echo "⚠️  有 $failed_tests 个测试失败"
        return 1
    fi
}

# 测试数据管理
manage_test_data() {
    local action="$1"
    echo "=== 测试数据管理 ==="
    cd "$SCRIPT_DIR"

    case "$action" in
        stats)
            echo "📊 显示数据统计..."
            python scripts/manage_test_data.py stats
            ;;
        cleanup)
            echo "🧹 清理测试数据..."
            python scripts/manage_test_data.py cleanup
            ;;
        backup)
            echo "💾 备份数据库..."
            python scripts/manage_test_data.py backup
            ;;
        *)
            echo "用法: $0 test-data {stats|cleanup|backup}"
            echo ""
            echo "  stats   - 显示测试数据和生产数据统计"
            echo "  cleanup - 清理所有测试数据"
            echo "  backup  - 备份数据库"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "AEC Pack Dispensing Inspection System 管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|test|test-apis|test-data|help}"
    echo ""
    echo "服务管理:"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  status    - 查看服务状态"
    echo ""
    echo "测试功能:"
    echo "  test      - 运行单元测试和集成测试"
    echo "  test-apis - 测试所有API接口"
    echo ""
    echo "数据管理:"
    echo "  test-data stats   - 显示测试数据统计"
    echo "  test-data cleanup - 清理测试数据"
    echo "  test-data backup  - 备份数据库"
    echo ""
    echo "其他:"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "文件:"
    echo "  PID文件: $PID_FILE"
    echo "  日志文件: $LOG_FILE"
    echo "  主脚本: $MAIN_SCRIPT"
    echo "  配置文件: $CONFIG_FILE"
    echo ""
    echo "示例:"
    echo "  $0 start              # 启动服务"
    echo "  $0 status             # 查看状态"
    echo "  $0 test-apis          # 测试所有API"
    echo "  $0 test-data stats    # 查看数据统计"
    echo "  $0 test-data cleanup  # 清理测试数据"
    echo ""
    echo "测试模式:"
    echo "  export AEC_TEST_MODE=true  # 启用测试模式"
    echo "  $0 start                   # 启动服务（测试模式）"
}

# 主逻辑
case "${1:-}" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        show_status
        ;;
    test)
        run_tests
        ;;
    test-apis)
        test_apis
        ;;
    test-data)
        manage_test_data "$2"
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        # 如果没有参数，默认启动服务（保持向后兼容）
        echo "未指定命令，默认启动服务..."
        start_server
        ;;
    *)
        echo "错误: 未知命令 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac