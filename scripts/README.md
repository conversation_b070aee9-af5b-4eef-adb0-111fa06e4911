# 数据管理脚本使用说明

## 数据删除脚本 (delete_data_by_date.py)

这个脚本可以根据指定日期删除所有相关数据，包括推理记录、检测结果、图像信息、统计数据等。

### 基本用法

```bash
# 查看指定日期的数据摘要（不删除）
python scripts/delete_data_by_date.py 2025-08-05 --summary-only

# 试运行模式（显示将要删除的数据，但不实际删除）
python scripts/delete_data_by_date.py 2025-08-05 --dry-run

# 删除指定日期的所有数据（包括图像文件）
python scripts/delete_data_by_date.py 2025-08-05

# 只删除数据库记录，保留图像文件
python scripts/delete_data_by_date.py 2025-08-05 --keep-images

# 指定数据库文件路径
python scripts/delete_data_by_date.py 2025-08-05 --db-path /path/to/database.db
```

### 参数说明

- `date`: 要删除的日期，格式为 YYYY-MM-DD
- `--summary-only`: 只显示数据摘要，不执行删除
- `--dry-run`: 试运行模式，显示将要删除的数据但不实际删除
- `--keep-images`: 保留图像文件，只删除数据库记录
- `--db-path`: 指定数据库文件路径（可选）

### 安全特性

1. **确认机制**: 实际删除前需要输入 'YES' 确认
2. **试运行模式**: 可以先查看将要删除的数据
3. **事务保护**: 数据库操作使用事务，失败时自动回滚
4. **详细日志**: 记录所有操作和错误信息

### 删除的数据类型

- 推理记录 (inference_records)
- 检测结果 (detection_results)  
- 图像信息 (image_info)
- 误报记录 (false_positive_reports)
- 统计摘要 (statistics_summary)
- 图像文件（原始图像和标注图像）

### 示例输出

```bash
$ python scripts/delete_data_by_date.py 2025-08-05 --summary-only

📊 2025-08-05 数据摘要:

📅 日期: 2025-08-05
📊 推理记录: 4 条
🎯 检测结果: 0 条
🖼️  图像信息: 0 条
❌ 误报记录: 1 条
📈 统计摘要: 0 条
📁 图像文件: 4 个

图像文件列表:
  1. original/2025/08/05/20250805_185636_31ea3551_original.jpg
  2. original/2025/08/05/20250805_185728_dc44a617_original.jpg
  3. original/2025/08/05/20250805_185814_57615ed7_original.jpg
  4. original/2025/08/05/20250805_185814_967ad341_original.jpg
```

### 注意事项

⚠️ **重要警告**: 
- 删除操作不可逆，请务必在删除前备份重要数据
- 建议先使用 `--dry-run` 或 `--summary-only` 查看将要删除的数据
- 删除操作会影响统计数据的准确性

### 常见使用场景

1. **清理测试数据**: 删除测试期间产生的数据
2. **数据维护**: 定期清理过期数据
3. **错误数据清理**: 删除有问题的推理记录
4. **存储空间管理**: 清理占用空间较大的历史数据

### 错误处理

如果脚本执行失败，请检查：
1. 数据库文件是否存在且可访问
2. 是否有足够的权限删除文件
3. 数据库是否被其他进程占用
4. 日期格式是否正确 (YYYY-MM-DD)

### 相关命令

```bash
# 查看服务状态
./run.sh status

# 查看数据库统计
python data_management_tool.py --stats

# 备份数据库
cp yolo_inference.db yolo_inference_backup_$(date +%Y%m%d).db
```
